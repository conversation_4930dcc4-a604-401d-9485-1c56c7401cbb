# <PERSON>

[![License](https://img.shields.io/github/license/hannahtrask/hannahtrask.svg)](LICENSE)
[![Last Commit](https://img.shields.io/github/last-commit/hannahtrask/hannahtrask.svg)](https://github.com/hannahtrask/hannahtrask/commits/main)

> ✨ Built with [V0](https://v0.dev), [Next.js](https://nextjs.org), and [Tailwind CSS](https://tailwindcss.com)

---

## 👋 About Me

Hey there! I'm <PERSON>, a freelance web developer with a passion for creating beautiful, functional websites for outdoor industry businesses, e-commerce ventures, and adventure-focused brands. When I'm not coding, you'll find me rock climbing, white water rafting, or exploring the great outdoors.

### What I Do

- **Specialization**: Web development for outdoor industry, e-commerce, adventure & wellness travel companies, yoga and wellness practitioners, and travel bloggers
- **Focus Areas**: Thoughtful lifestyle brands, small e-commerce businesses, creative studios, and values-driven teams
- **Preferred Platforms**: WordPress, Shopify, and custom React/Next.js applications
- **Design Philosophy**: Clean, nature-inspired designs with smooth animations and excellent user experience

### Let's Connect

- 📧 Business inquiries: <EMAIL>
- 🌐 Portfolio: This very website you're looking at!

---

## 🛠 Complete Tech Stack

### Frontend Framework & Core

- **Next.js 15** – React framework with App Router, SSR, and file-based routing
- **React 19** – Latest React with concurrent features
- **TypeScript** – Type-safe JavaScript for better development experience

### Styling & Design

- **Tailwind CSS** – Utility-first CSS framework for rapid UI development
- **CSS Variables** – Custom design tokens for consistent theming
- **Google Fonts** – Playfair Display & Montserrat for typography
- **Custom Gradients** – Desert-inspired color schemes with gradient backgrounds

### UI Components & Interactions

- **shadcn/ui** – High-quality, accessible React components
- **Radix UI** – Unstyled, accessible UI primitives
  - `@radix-ui/react-dropdown-menu`
  - `@radix-ui/react-label`
  - `@radix-ui/react-slot`
- **Framer Motion** – Smooth animations and scroll-triggered effects
- **Tailwind CSS Animate** – Additional animation utilities

### Forms & Validation

- **React Hook Form** – Performant forms with easy validation
- **Zod** – TypeScript-first schema validation
- **@hookform/resolvers** – Integration between React Hook Form and Zod

### Icons & Assets

- **Lucide React** – Beautiful, customizable SVG icons
- **Simple Icons** – Brand icons for technology badges
- **Custom SVG Icons** – Hand-crafted icons for specific use cases

### Email & Communication

- **Resend** – Modern email API for contact form submissions
- **Sonner** – Beautiful toast notifications with custom styling

### Theming & Accessibility

- **next-themes** – Dark/light mode with system preference detection
- **Class Variance Authority** – Type-safe component variants
- **clsx & tailwind-merge** – Conditional CSS class utilities

### Development Tools

- **ESLint** – Code linting with TypeScript, React, and accessibility rules
- **Prettier** – Code formatting with custom configuration (no semicolons!)
- **PostCSS** – CSS processing with Tailwind CSS integration
- **Autoprefixer** – Automatic vendor prefixes for CSS

### Build & Deployment

- **Next.js Build System** – Optimized production builds
- **Image Optimization** – Next.js Image component with custom configuration
- **TypeScript Compiler** – Type checking and compilation

### Animation Features

- **Scroll-triggered Animations** – Custom hook for intersection observer-based animations
- **Parallax Effects** – Smooth parallax scrolling on project cards
- **Slide-in Animations** – Framer Motion powered entrance animations
- **Hover Effects** – CSS transitions and transforms for interactive elements

---
